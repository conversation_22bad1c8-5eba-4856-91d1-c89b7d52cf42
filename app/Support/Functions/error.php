<?php

/*
|--------------------------------------------------------------------------
| Error helpers
|--------------------------------------------------------------------------
|
*/

if (!function_exists('error')) {
    /**
     * Write some error to the log.
     *
     * @param  string $message
     * @param  array $context
     * @return void
     */
    function error($message, $context = [])
    {
        app('log')->error($message, $context);
    }
}

/**
 * Log and throw a specific error.
 *
 * @param  \Throwable $th
 * @param  string|null $message
 * @param  bool $log
 * @return never
 */
function throw_error(\Throwable $th, ?string $message = null, bool $log = true): never
{
    if ($log) {
        error($th);
    }

    throw new \Exception($message ?? __('general.unknown_error'));
}

/**
 * Log and throw a generic unknown error.
 *
 * @param  \Throwable $th
 * @return void
 */
function throwUnknownError(\Throwable $th): void
{
    throw_error($th);
}

/**
 * Log and throw a generic CLM Online integration error.
 *
 * @param  \Throwable $th
 * @return void
 */
function throwClmOnlineError(\Throwable $th): void
{
    throw_error($th, __('general.clm_online_integration_error'));
}

/**
 * Log and throw a generic SOC integration error.
 *
 * @param  \Throwable $th
 * @return void
 */
function throwSocError(\Throwable $th): void
{
    throw_error($th, __('general.soc_integration_error'));
}

function structure_log(string $className, mixed $logContents)
{
    error('[SL] ' . $className . "\n" . $logContents);
}
