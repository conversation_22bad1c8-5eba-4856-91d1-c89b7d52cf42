<?php

namespace App\Actions\Reports;

use App\Core\Actions\Interfaces\Reportable;
use App\Core\Http\Requests\ReportActionRequest;
use App\Core\Module;
use App\Core\Reports\Core\BaseReport;
use App\Exports\Reports\ContractsExport;
use App\Models\Contract;
use App\Models\ContractItem;
use App\Support\Dictionaries\ReportDictionary;
use App\Support\Helpers\ReportHelper;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Lorisleiva\Actions\Concerns\AsAction;
use Throwable;

class GenerateContractsReport extends BaseReport implements Reportable
{
    use AsAction;

    public function asController(ReportActionRequest $request): mixed
    {
        if ($request->comesFromGetMethod()) {
            return LoadReportView::run(ReportDictionary::CONTRACTS);
        }

        $this->currentFormat = $request->getReportFormat();
        $this->excelFileName = 'contratos';
        $this->moduleName = Module::CONTRACT;
        $this->reportName = ReportDictionary::CONTRACTS;
        $this->reportExportClassName = ContractsExport::class;
        $this->notificationSubjectReportName = 'relatório de contratos';
        $this->notificationBodyReportEntity = 'contratos';

        try {
            $this->reportData = $this->handle(
                $request->input('format'),
                $request->input('contract_type_id'),
                $request->input('index_id'),
                $request->input('company_id'),
                $request->input('salesman_id'),
                $request->input('after_sales_salesman_id'),
                $request->input('status'),
                $request->input('installment_count'),
                $request->input('payment_term'),
                $request->input('billing_expiration_day'),
                $request->input('week_expiration_day'),
                $request->input('service_tax_code'),
                carbon($request->input('started_at_from')),
                carbon($request->input('started_at_to')),
                carbon($request->input('ended_at_from')),
                carbon($request->input('ended_at_to')),
                carbon($request->input('signed_at_from')),
                carbon($request->input('signed_at_to')),
                carbon($request->input('cancelled_at_from')),
                carbon($request->input('cancelled_at_to')),
                carbon($request->input('created_at_from')),
                carbon($request->input('created_at_to'))
            );
        } catch (Throwable $th) {
            return redirect_report_error();
        }

        return $this->getReport();
    }

    public function handle(
        string $format,
        ?int $contractTypeId = null,
        ?int $indexId = null,
        ?int $companyId = null,
        ?int $salesmanId = null,
        ?int $afterSalesSalesmanId = null,
        ?string $status = null,
        ?int $installmentCount = null,
        ?int $paymentTerm = null,
        ?int $billingExpirationDay = null,
        ?int $weekExpirationDay = null,
        ?string $serviceTaxCode = null,
        ?Carbon $startedAtFrom = null,
        ?Carbon $startedAtTo = null,
        ?Carbon $endedAtFrom = null,
        ?Carbon $endedAtTo = null,
        ?Carbon $signedAtFrom = null,
        ?Carbon $signedAtTo = null,
        ?Carbon $cancelledAtFrom = null,
        ?Carbon $cancelledAtTo = null,
        ?Carbon $createdAtFrom = null,
        ?Carbon $createdAtTo = null
    ): array {
        return ReportHelper::isScreenable($format)
            ? $this->buildPDFData(
                $contractTypeId,
                $indexId,
                $companyId,
                $salesmanId,
                $afterSalesSalesmanId,
                $status,
                $installmentCount,
                $paymentTerm,
                $billingExpirationDay,
                $weekExpirationDay,
                $serviceTaxCode,
                $startedAtFrom,
                $startedAtTo,
                $endedAtFrom,
                $endedAtTo,
                $signedAtFrom,
                $signedAtTo,
                $cancelledAtFrom,
                $cancelledAtTo,
                $createdAtFrom,
                $createdAtTo
            )
            : $this->buildExcelData(
                $contractTypeId,
                $indexId,
                $companyId,
                $salesmanId,
                $afterSalesSalesmanId,
                $status,
                $installmentCount,
                $paymentTerm,
                $billingExpirationDay,
                $weekExpirationDay,
                $serviceTaxCode,
                $startedAtFrom,
                $startedAtTo,
                $endedAtFrom,
                $endedAtTo,
                $signedAtFrom,
                $signedAtTo,
                $cancelledAtFrom,
                $cancelledAtTo,
                $createdAtFrom,
                $createdAtTo
            );
    }

    public function buildPDFData(mixed ...$params): array
    {
        $contractTypeId = $params[0];
        $indexId = $params[1];
        $companyId = $params[2];
        $salesmanId = $params[3];
        $afterSalesSalesmanId = $params[4];
        $status = $params[5];
        $installmentCount = $params[6];
        $paymentTerm = $params[7];
        $billingExpirationDay = $params[8];
        $weekExpirationDay = $params[9];
        $serviceTaxCode = $params[10];
        $startedAtFrom = $params[11];
        $startedAtTo = $params[12];
        $endedAtFrom = $params[13];
        $endedAtTo = $params[14];
        $signedAtFrom = $params[15];
        $signedAtTo = $params[16];
        $cancelledAtFrom = $params[17];
        $cancelledAtTo = $params[18];
        $createdAtFrom = $params[19];
        $createdAtTo = $params[20];

        $lines = $this->buildReportLines(
            $contractTypeId,
            $indexId,
            $companyId,
            $salesmanId,
            $afterSalesSalesmanId,
            $status,
            $installmentCount,
            $paymentTerm,
            $billingExpirationDay,
            $weekExpirationDay,
            $serviceTaxCode,
            $startedAtFrom,
            $startedAtTo,
            $endedAtFrom,
            $endedAtTo,
            $signedAtFrom,
            $signedAtTo,
            $cancelledAtFrom,
            $cancelledAtTo,
            $createdAtFrom,
            $createdAtTo,
        );

        return [
            'lines' => $lines
        ];
    }

    public function buildExcelData(mixed ...$params): array
    {
        $contractTypeId = $params[0];
        $indexId = $params[1];
        $companyId = $params[2];
        $salesmanId = $params[3];
        $afterSalesSalesmanId = $params[4];
        $status = $params[5];
        $installmentCount = $params[6];
        $paymentTerm = $params[7];
        $billingExpirationDay = $params[8];
        $weekExpirationDay = $params[9];
        $serviceTaxCode = $params[10];
        $startedAtFrom = $params[11];
        $startedAtTo = $params[12];
        $endedAtFrom = $params[13];
        $endedAtTo = $params[14];
        $signedAtFrom = $params[15];
        $signedAtTo = $params[16];
        $cancelledAtFrom = $params[17];
        $cancelledAtTo = $params[18];
        $createdAtFrom = $params[19];
        $createdAtTo = $params[20];

        return $this->buildReportLines(
            $contractTypeId,
            $indexId,
            $companyId,
            $salesmanId,
            $afterSalesSalesmanId,
            $status,
            $installmentCount,
            $paymentTerm,
            $billingExpirationDay,
            $weekExpirationDay,
            $serviceTaxCode,
            $startedAtFrom,
            $startedAtTo,
            $endedAtFrom,
            $endedAtTo,
            $signedAtFrom,
            $signedAtTo,
            $cancelledAtFrom,
            $cancelledAtTo,
            $createdAtFrom,
            $createdAtTo,
        );
    }

    public function buildReportLines(
        ?int $contractTypeId = null,
        ?int $indexId = null,
        ?int $companyId = null,
        ?int $salesmanId = null,
        ?int $afterSalesSalesmanId = null,
        ?string $status = null,
        ?int $installmentCount = null,
        ?int $paymentTerm = null,
        ?int $billingExpirationDay = null,
        ?int $weekExpirationDay = null,
        ?string $serviceTaxCode = null,
        ?Carbon $startedAtFrom = null,
        ?Carbon $startedAtTo = null,
        ?Carbon $endedAtFrom = null,
        ?Carbon $endedAtTo = null,
        ?Carbon $signedAtFrom = null,
        ?Carbon $signedAtTo = null,
        ?Carbon $cancelledAtFrom = null,
        ?Carbon $cancelledAtTo = null,
        ?Carbon $createdAtFrom = null,
        ?Carbon $createdAtTo = null
    ): array {
        try {
            return ContractItem::query()
                ->with([
                    'contract.contractType:id,name',
                    'contract.index:id,name',
                    'contract.company:id,name,trading_name,tax_id_number,branch_name',
                    'contract.salesman:id,name',
                    'contract.afterSalesSalesman:id,name',
                    'procedure:id,name'
                ])
                ->whereHas('contract', function (Builder $query) {
                    return $query->forReporting();
                })
                ->when(!is_null($contractTypeId), function (Builder $query) use ($contractTypeId) {
                    return $query->whereHas('contract', function (Builder $query) use ($contractTypeId) {
                        return $query->where('contract_type_id', $contractTypeId);
                    });
                })
                ->when(!is_null($indexId), function (Builder $query) use ($indexId) {
                    return $query->whereHas('contract', function (Builder $query) use ($indexId) {
                        return $query->where('index_id', $indexId);
                    });
                })
                ->when(!is_null($companyId), function (Builder $query) use ($companyId) {
                    return $query->whereHas('contract', function (Builder $query) use ($companyId) {
                        return $query->where('company_id', $companyId);
                    });
                })
                ->when(!is_null($salesmanId), function (Builder $query) use ($salesmanId) {
                    return $query->whereHas('contract', function (Builder $query) use ($salesmanId) {
                        return $query->where('salesman_id', $salesmanId);
                    });
                })
                ->when(!is_null($afterSalesSalesmanId), function (Builder $query) use ($afterSalesSalesmanId) {
                    return $query->whereHas('contract', function (Builder $query) use ($afterSalesSalesmanId) {
                        return $query->where('after_sales_salesman_id', $afterSalesSalesmanId);
                    });
                })
                ->when(!is_null($status), function (Builder $query) use ($status) {
                    return $query->whereHas('contract', function (Builder $query) use ($status) {
                        return $query->where('status', $status);
                    });
                })
                ->when(!is_null($installmentCount), function (Builder $query) use ($installmentCount) {
                    return $query->whereHas('contract', function (Builder $query) use ($installmentCount) {
                        return $query->where('installment_count', $installmentCount);
                    });
                })
                ->when(!is_null($paymentTerm), function (Builder $query) use ($paymentTerm) {
                    return $query->whereHas('contract', function (Builder $query) use ($paymentTerm) {
                        return $query->where('payment_term', $paymentTerm);
                    });
                })
                ->when(!is_null($billingExpirationDay), function (Builder $query) use ($billingExpirationDay) {
                    return $query->whereHas('contract', function (Builder $query) use ($billingExpirationDay) {
                        return $query->where('billing_expiration_day', $billingExpirationDay);
                    });
                })
                ->when(!is_null($weekExpirationDay), function (Builder $query) use ($weekExpirationDay) {
                    return $query->whereHas('contract', function (Builder $query) use ($weekExpirationDay) {
                        return $query->where('week_expiration_day', $weekExpirationDay);
                    });
                })
                ->when(!is_null($serviceTaxCode), function (Builder $query) use ($serviceTaxCode) {
                    return $query->whereHas('contract', function (Builder $query) use ($serviceTaxCode) {
                        return $query->where('service_tax_code', $serviceTaxCode);
                    });
                })
                ->when(!is_null($startedAtFrom), function (Builder $query) use ($startedAtFrom) {
                    return $query->whereHas('contract', function (Builder $query) use ($startedAtFrom) {
                        return $query->where('started_at', '>=', $startedAtFrom->format('Y-m-d'));
                    });
                })
                ->when(!is_null($startedAtTo), function (Builder $query) use ($startedAtTo) {
                    return $query->whereHas('contract', function (Builder $query) use ($startedAtTo) {
                        return $query->where('started_at', '<=', $startedAtTo->format('Y-m-d'));
                    });
                })
                ->when(!is_null($endedAtFrom), function (Builder $query) use ($endedAtFrom) {
                    return $query->whereHas('contract', function (Builder $query) use ($endedAtFrom) {
                        return $query->where('ended_at', '>=', $endedAtFrom->format('Y-m-d'));
                    });
                })
                ->when(!is_null($endedAtTo), function (Builder $query) use ($endedAtTo) {
                    return $query->whereHas('contract', function (Builder $query) use ($endedAtTo) {
                        return $query->where('ended_at', '<=', $endedAtTo->format('Y-m-d'));
                    });
                })
                ->when(!is_null($signedAtFrom), function (Builder $query) use ($signedAtFrom) {
                    return $query->whereHas('contract', function (Builder $query) use ($signedAtFrom) {
                        return $query->where('signed_at', '>=', $signedAtFrom->format('Y-m-d'));
                    });
                })
                ->when(!is_null($signedAtTo), function (Builder $query) use ($signedAtTo) {
                    return $query->whereHas('contract', function (Builder $query) use ($signedAtTo) {
                        return $query->where('signed_at', '<=', $signedAtTo->format('Y-m-d'));
                    });
                })
                ->when(!is_null($cancelledAtFrom), function (Builder $query) use ($cancelledAtFrom) {
                    return $query->whereHas('contract', function (Builder $query) use ($cancelledAtFrom) {
                        return $query->where('cancelled_at', '>=', $cancelledAtFrom->format('Y-m-d'));
                    });
                })
                ->when(!is_null($cancelledAtTo), function (Builder $query) use ($cancelledAtTo) {
                    return $query->whereHas('contract', function (Builder $query) use ($cancelledAtTo) {
                        return $query->where('cancelled_at', '<=', $cancelledAtTo->format('Y-m-d'));
                    });
                })
                ->when(!is_null($createdAtFrom), function (Builder $query) use ($createdAtFrom) {
                    return $query->whereHas('contract', function (Builder $query) use ($createdAtFrom) {
                        return $query->where('created_at', '>=', $createdAtFrom->format('Y-m-d'));
                    });
                })
                ->when(!is_null($createdAtTo), function (Builder $query) use ($createdAtTo) {
                    return $query->whereHas('contract', function (Builder $query) use ($createdAtTo) {
                        return $query->where('created_at', '<=', $createdAtTo->format('Y-m-d'));
                    });
                })
                ->get()
                ->sortBy(['contract_id', 'procedure.name'])
                ->map(function (ContractItem $contractItem) {
                    return [
                        'id' => $contractItem['contract']['id'],
                        'contract_type' => $contractItem['contract']['contractType']['name'],
                        'company_name' => $contractItem['contract']['company']['name'],
                        'company_trading_name' => $contractItem['contract']['company']['trading_name'],
                        'company_friendly_tax_id_number' => $contractItem['contract']['company']['friendly_tax_id_number'],
                        'company_branch_name' => $contractItem['contract']['company']['branch_name'],
                        'salesman_name' => $contractItem['contract']['salesman']['name'] ?? '',
                        'after_sales_salesman_name' => $contractItem['contract']['after_sales_salesman']['name'] ?? '',
                        'index_name' => $contractItem['contract']['index']['name'] ?? '',
                        'created_at' => format_datetime($contractItem['contract']['created_at']),
                        'started_at' => format_date($contractItem['contract']['started_at']),
                        'ended_at' => format_date($contractItem['contract']['ended_at']),
                        'signed_at' => format_date($contractItem['contract']['signed_at']),
                        'last_suspended_at' => format_date($contractItem['contract']['last_suspended_at']),
                        'cancelled_at' => format_date($contractItem['contract']['cancelled_at']),
                        'reason_name' => $contractItem['contract']['reason']['name'] ?? '',
                        'installment_count' => $contractItem['contract']['installment_count'],
                        'payment_term' => $contractItem['contract']['payment_term'],
                        'billing_expiration_day' => $contractItem['contract']['billing_expiration_day'],
                        'week_expiration_day' => $contractItem['contract']['week_expiration_day'],
                        'additional_info' => $contractItem['contract']['additional_info'],
                        'billing_additional_info' => $contractItem['contract']['billing_additional_info'],
                        'service_tax_code' => $contractItem['contract']['service_tax_code'],
                        'status' => match ($contractItem['contract']['status']) {
                            Contract::STATUS_ACTIVE => 'Ativo',
                            Contract::STATUS_CANCELLED => 'Cancelado',
                            Contract::STATUS_EXPIRED => 'Expirado',
                            Contract::STATUS_SUSPENDED => 'Suspenso'
                        },
                        'procedure_name' => $contractItem['procedure']['name'],
                        'quantity' => $contractItem['quantity'],
                        'amount' => $contractItem['friendly_amount'],
                        'minimum_amount' => $contractItem['friendly_minimum_amount']
                    ];
                })
                ->toArray();
        } catch (Throwable $th) {
            throw_error($th);
        }
    }
}
